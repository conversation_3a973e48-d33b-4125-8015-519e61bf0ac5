import aiohttp
import re
import json
import shlex
import os
import asyncio
from urllib.parse import urlparse
from astrbot.api.event import filter, AstrMessageEvent
from astrbot.api.star import Context, Star, register
from astrbot.api.message_components import Plain, Image
from astrbot.api import logger

# ### <<< 修改部分 开始 ###
# 1. 修改了SUMMARY_PROMPT，使其更适合于解释授权码信息
SUMMARY_PROMPT = """
你自称小真寻。请根据下面的API响应，并按指定的格式进行回复：
返回的主要数据内容应该会有：accessCode和expiresAt

请使用中文回答，保持可爱、专业、简洁，就像二次元少女那样。

示例回复格式：
查到啦！
使用码: 1A2B3C4D
12-31 23:59:59 前有效哦~

API响应内容:
```
{content}
```
"""

# 2. 新增API配置常量
# !!!重要!!! 请将 'http://your_api_address.com' 替换为你的实际API地址
API_URL = "https://osound.ykload.com/api/get-use-code" 
API_TOKEN = "OSound798999."
# ### <<< 修改部分 结束 ###


# 配置常量
MAX_RESPONSE_SIZE = 10 * 1024 * 1024  # 10MB响应限制
MAX_RETRIES = 3  # 最大重试次数
RETRY_DELAY = 1  # 初始重试延迟(秒)


async def send_request(url, method="GET", params=None, data=None, headers=None, cookies=None):
    """发送HTTP请求并返回结果，支持自动重试和响应大小限制"""
    retries = 0
    last_error = None
    
    while retries <= MAX_RETRIES:
        try:
            async with aiohttp.ClientSession(cookies=cookies) as session:
                kwargs = {
                    'params': params,
                    'headers': headers,
                    'timeout': aiohttp.ClientTimeout(total=10)
                }
                
                if data:
                    if isinstance(data, dict):
                        kwargs['json'] = data
                    else:
                        kwargs['data'] = data
                
                http_method = getattr(session, method.lower(), None)
                if not http_method:
                    return {"success": False, "message": f"不支持的请求方法: {method}"}
                
                async with http_method(url, **kwargs) as response:
                    response.raise_for_status()
                    
                    content_length = response.headers.get('Content-Length')
                    if content_length and int(content_length) > MAX_RESPONSE_SIZE:
                        return {
                            "success": False, 
                            "message": f"响应数据过大: {int(content_length)/1024/1024:.2f}MB (最大限制: {MAX_RESPONSE_SIZE/1024/1024}MB)",
                            "status_code": response.status
                        }
                    
                    data = bytearray()
                    chunk_size = 8192
                    async for chunk in response.content.iter_chunked(chunk_size):
                        data.extend(chunk)
                        if len(data) > MAX_RESPONSE_SIZE:
                            return {
                                "success": False, 
                                "message": f"响应数据超过限制: {MAX_RESPONSE_SIZE/1024/1024}MB",
                                "status_code": response.status
                            }
                    
                    try:
                        result = json.loads(data.decode('utf-8'))
                        return {"success": True, "data": result, "status_code": response.status}
                    except:
                        return {"success": True, "data": data.decode('utf-8'), "status_code": response.status}
        
        except aiohttp.ClientResponseError as http_err:
            return {"success": False, "message": f"HTTP错误: {http_err}", "status_code": http_err.status}
        except (aiohttp.ClientConnectorError, aiohttp.ServerTimeoutError, aiohttp.ClientOSError) as e:
            retries += 1
            last_error = e
            if retries <= MAX_RETRIES:
                await asyncio.sleep(RETRY_DELAY * retries)
                logger.info(f"重试请求({retries}/{MAX_RETRIES}): {url}")
                continue
            else:
                return {"success": False, "message": f"请求失败(已重试{MAX_RETRIES}次): {str(last_error)}"}
        except Exception as e:
            return {"success": False, "message": f"请求错误: {str(e)}"}

# ### <<< 修改部分 开始 ###
# 3. 删除了不再需要的 parse_curl_command 函数
# ### <<< 修改部分 结束 ###

@register("http_request", "wayzinx", "授权查询插件", "2.0.0") # 修改了插件描述和版本
class HttpRequestPlugin(Star):
    def __init__(self, context: Context):
        super().__init__(context)

    async def summarize_response(self, result, session_id):
        """使用LLM总结HTTP响应内容"""
        if not result["success"]:
            # 对用户更友好地展示错误信息
            if result.get("status_code"):
                 return f"请求失败: 目标服务器返回错误 (状态码: {result['status_code']})。\n详情: {result['message']}"
            return f"请求失败: {result['message']}"

        response_content = str(result["data"])
        
        # 调用LLM生成总结内容
        try:
            prompt = SUMMARY_PROMPT.format(content=response_content)
            llm_response = await self.context.get_using_provider().text_chat(
                prompt=prompt,
                session_id=f"{session_id}_http_summary"
            )
            
            # 直接返回LLM的总结
            return llm_response.completion_text
        except Exception as e:
            logger.error(f"总结响应内容时出错: {e}")
            # 如果总结失败，返回格式化的原始信息
            data = result.get('data', {})
            if isinstance(data, dict):
                code = data.get('accessCode', '未获取到')
                expires = data.get('expiresAt', '未获取到')
                return f"AI总结失败，为您展示原始信息：\n- 使用码: {code}\n- 到期时间: {expires}"
            else:
                return f"AI总结失败，为您展示原始信息：\n{str(data)[:1000]}"

    # ### <<< 修改部分 开始 ###
    # 4. 移除了 get, post, request, curl/请求 等所有旧的命令处理函数
    #    新增了一个专门用于查询授权的命令处理函数
    
    @filter.command("，使用码，欧内盖~", "auth")
    async def query_auth_code(self, event: AstrMessageEvent):
        """发送固定请求到API以获取使用码和到期时间，并总结响应。"""

        yield event.chain_result([Plain("好哒，很快喔~")])

        # 准备请求头
        headers = {
            'Authorization': f'Bearer {API_TOKEN}'
        }

        # 发送固定的GET请求
        result = await send_request(
            url=API_URL,
            method="GET",
            headers=headers
        )

        # 获取AI总结后的回复
        response_text = await self.summarize_response(result, event.session_id)

        # 将最终结果发送给用户
        yield event.chain_result([Plain(response_text)])

    @filter.command("，登录码，欧内盖~", "auth")
    async def register_user(self, event: AstrMessageEvent):
        """发送POST请求到注册API进行用户注册"""

        yield event.chain_result([Plain("正在为您注册，请稍等~")])

        # 准备请求数据
        request_data = {
            'token': 'Seeq233UserManege',
            'qq': str(event.sender.id)  # 使用发送消息的用户QQ号
        }

        # 准备请求头
        headers = {
            'Content-Type': 'application/json'
        }

        # 发送POST请求到注册API
        result = await send_request(
            url='https://seeq.ykload.com/api/register',  # 根据需要修改为实际的注册API地址
            method="POST",
            data=request_data,
            headers=headers
        )

        # 获取AI总结后的回复
        response_text = await self.summarize_response(result, event.session_id)

        # 将最终结果发送给用户
        yield event.chain_result([Plain(response_text)])

    # ### <<< 修改部分 结束 ###

    async def terminate(self):
        pass